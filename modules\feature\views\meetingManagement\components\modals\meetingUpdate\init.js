import { omit } from 'lodash';
import { getSelectedLabel } from 'feature/views/meetingManagement/commonFunction';
import moment from 'moment';

// 表单初始数据
export const form = {
    // 会议名称
    meetingTitle: '',
    // 是否首次会议
    isFirst: '',
    // 关联首次会议
    associateMeetingId: '',
    // 会议类型
    meetingType: '',
    // 评审组织形式
    organizeForm: '',
    // 会议材料
    materialsGrantFlag: '',
    // 材料发放途径
    materialsGrantMode: '',
    // 会议材料网上共享地址
    meetingMaterials: '',
    // 材料发放计划
    materialsGrantPlan: '',
    // 会议日期
    date: '',
    // 会议开始、结束时间
    timeRange: '',
    // 会议地点
    meetingLocation: [],
    // 会议组织人
    organizerAccount: '',
    // 主持人
    hostAccount: '',
    // 会议纪要编制人
    minutesWriterAccount: '',
    // 会议纪要审核人
    minutesReviewerAccount: '',
    // 关联项目
    assProjectIdList: [],
    // 会议议程
    meetingAgenda: '',
    // 会议状态
    meetingStatus: '待召开',
    // 会议纪要状态
    minutesStatus: '',
    // 参会评委列表
    reviewerList: [
        {
            // 会议角色
            meetingRole: '评委',
            // 评委角色
            userRole: '评审决策人',
            // 评委姓名
            userAccount: '',
            // 参会情况
            attendanceStatus: '参会',
            // 替代人
            replaceUserAccount: ''
        }
    ],
    // 参会人员列表
    participantList: [
        {
            // 参会人员角色
            meetingRole: '参会人员',
            // 参会人员角色
            userRole: '参会人员',
            // 参会人员姓名
            userAccount: '',
            // 参会情况
            attendanceStatus: '参会',
            // 替代人
            replaceUserAccount: ''
        }
    ],
    // 会议白名单人员
    whiteList: []
};

/**
 * 处理表单数据,转换为后端接口需要的数据
 * @param {Object} data 表单数据
 * @param {Scope} scope this
 * @return {Object} 处理后的数据
 */
// eslint-disable-next-line max-lines-per-function
export const handleFormData = (data, scope) => {
    const {
        assProjectIdList,
        date,
        timeRange,
        participantList,
        organizerAccount,
        hostAccount,
        minutesWriterAccount,
        minutesReviewerAccount,
        whiteList,
        meetingLocation,
        meetingId,
        ...restData
    } = data;
    let { reviewerList } = data;
    // 必须是外部传入的会议id
    restData.meetingId = scope.meetingId;

    // 会议地点非线上的时候是多选,否则是项目管理信息化平台
    if (Array.isArray(meetingLocation)) {
        restData.meetingLocation = meetingLocation.join(',');
    } else {
        restData.meetingLocation = '项目管理信息化平台';
    }
    // 处理日期与时间
    if (date && timeRange) {
        restData.date = moment(date).format('YYYY-MM-DD');
        restData.meetingDate = restData.date;
        restData.startTime = timeRange[0];
        restData.endTime = timeRange[1];
    } else {
        restData.meetingDate = '';
        restData.startTime = '';
        restData.endTime = '';
    }
    // 评审组织形是线上，如果没填时间，那么开始时间就是当前的时间
    if (data.organizeForm === '线上' && !restData.startTime) {
        const now = moment(new Date()).format('HH:mm');
        restData.meetingDate = moment(new Date()).format('YYYY-MM-DD');
        restData.startTime = now;
        restData.endTime = '';
    }
    // 评审组织形式没填的时候，传线下
    if (!data.organizeForm) {
        restData.organizeForm = '线下';
    }
    // 处理评委列表,获取姓名，非评审会议没有评委
    if (!scope.meetingClass || scope.meetingClass === '一般会议') {
        reviewerList = [];
    }
    reviewerList.forEach((item, index) => {
        if (!item.userAccount) {
            return;
        }
        item.userName = getSelectedLabel(scope.$refs[`reviewer${index}`][0]);
        if (!item.replaceUserAccount) {
            return;
        }
        item.replaceUserName = getSelectedLabel(
            scope.$refs[`reviewerAlternative${index}`][0]
        );
    });

    // 处理参会人员列表，滤除空的，获取姓名
    const filteredParticipantList = participantList.filter(
        (i) => i.userAccount
    );
    filteredParticipantList.forEach((item, index) => {
        if (!item.userAccount) {
            return;
        }
        item.userName = getSelectedLabel(scope.$refs[`participant${index}`][0]);

        if (!item.replaceUserAccount) {
            return;
        }
        item.replaceUserName = getSelectedLabel(
            scope.$refs[`participantAlternative${index}`][0]
        );
    });

    let whiteWholeList = [];
    // 处理会议白名单（邮件抄送人）列表，获取姓名
    if (whiteList.length > 0) {
        const whiteListLabel = getSelectedLabel(scope.$refs.whiteList);
        whiteWholeList = data.whiteList.map((userAccount, index) => {
            return {
                meetingRole: '白名单人员',
                userRole: '白名单人员',
                userAccount,
                userName: whiteListLabel[index],
                attendanceStatus: '白名单'
            };
        });
    }
    const meetingSuperviseList = [
        {
            meetingRole: '会议组织人',
            userRole: '会议组织人',
            userAccount: organizerAccount,
            userName: getSelectedLabel(scope.$refs.organizerAccount),
            attendanceStatus: '参会'
        },
        {
            meetingRole: '主持人',
            userRole: '主持人',
            userAccount: hostAccount,
            userName: getSelectedLabel(scope.$refs.hostAccount),
            attendanceStatus: '参会'
        },
        {
            meetingRole: '会议纪要编制人',
            userRole: '会议纪要编制人',
            userAccount: minutesWriterAccount,
            userName: getSelectedLabel(scope.$refs.minutesWriterAccount),
            attendanceStatus: '参会'
        },
        {
            meetingRole: '会议纪要审核人',
            userRole: '会议纪要审核人',
            userAccount: minutesReviewerAccount,
            userName: getSelectedLabel(scope.$refs.minutesReviewerAccount),
            attendanceStatus: '参会'
        }
    ];
    const res = omit(
        {
            assProjectIdList,
            meetingInfo: {
                meetingClass: scope.meetingClass,
                ...restData
            },
            meetingPartRelateList: [
                ...reviewerList,
                ...filteredParticipantList,
                ...whiteWholeList,
                ...meetingSuperviseList
            ]
        },
        ['meetingInfo.reviewerList']
    );
    return res;
};

/**
 * 处理后端接口数据,转换为表单需要的数据
 * @param {Object} data 后端接口数据
 * @return {Object} 处理后的表单数据
 */
// eslint-disable-next-line max-lines-per-function
export const handleBackendData = (data) => {
    const { assProjectIdList, meetingInfo, meetingPartRelateList } = data;

    const {
        startTime,
        endTime,
        associateMeetingId,
        meetingDate,
        meetingLocation,
        ...restData
    } = meetingInfo;

    if (meetingLocation && meetingLocation !== '项目管理信息化平台') {
        restData.meetingLocation = meetingLocation.split(',');
    }
    // 处理日期与时间
    let dateValue = null;
    let timeRange = null;
    // 线上会议只有开始时间，并且不需要显示时间区间
    if (startTime) {
        dateValue = meetingDate;
    }
    if (startTime && endTime) {
        timeRange = [startTime, endTime];
    }

    // 处理人员相关
    let reviewerList = meetingPartRelateList.filter(
        (i) => i.meetingRole === '评委'
    );
    // 如果评委为空，赋初始值
    if (reviewerList.length === 0) {
        reviewerList = [
            {
                // 会议角色
                meetingRole: '评委',
                // 评委角色
                userRole: '',
                // 评委姓名
                userAccount: '',
                // 参会情况
                attendanceStatus: '参会',
                // 替代人
                replaceUserAccount: ''
            }
        ];
    }
    const participantList = meetingPartRelateList.filter(
        (i) => i.meetingRole === '参会人员'
    );
    const whiteList = meetingPartRelateList
        .filter((i) => i.meetingRole === '白名单人员')
        .map((i) => i.userAccount);

    const organizerAccount = meetingPartRelateList.filter(
        (i) => i.meetingRole === '会议组织人'
    )[0].userAccount;
    const hostAccount = meetingPartRelateList.filter(
        (i) => i.meetingRole === '主持人'
    )[0].userAccount;
    const minutesWriterAccount = meetingPartRelateList.filter(
        (i) => i.meetingRole === '会议纪要编制人'
    )[0].userAccount;
    const minutesReviewerAccount = meetingPartRelateList.filter(
        (i) => i.meetingRole === '会议纪要审核人'
    )[0].userAccount;

    // 判断是否首次会议
    const isFirst = !associateMeetingId;
    // 构建表单数据
    const formData = {
        assProjectIdList,
        date: dateValue,
        timeRange,
        reviewerList,
        participantList,
        whiteList,
        organizerAccount,
        hostAccount,
        minutesWriterAccount,
        minutesReviewerAccount,
        associateMeetingId,
        isFirst,
        startTime,
        endTime,
        ...restData
    };

    return formData;
};

/**
 * 会议信息校验
 * @param {Scope} scope this
 */
export const validateForm = async (scope) => {
    // 会议信息校验
    let formValid = true;
    await scope.$refs.form.validate((valid) => {
        if (!valid) {
            formValid = false;
        }
    });
    // 参会人员校验
    let participantValid = true;
    await Promise.all(
        scope.$refs.participantForm.map(async (f) => {
            try {
                await f.validate();
            } catch (e) {
                participantValid = false;
            }
        })
    );

    // 没有评委的时候，不校验
    if (!scope.$refs.reviewerForm) return formValid && participantValid;
    let reviewerValid = true;
    await Promise.all(
        scope.$refs.reviewerForm.map(async (f) => {
            try {
                await f.validate();
            } catch (e) {
                reviewerValid = false;
            }
        })
    );

    return formValid && reviewerValid && participantValid;
};
