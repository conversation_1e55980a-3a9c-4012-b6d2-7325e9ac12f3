<!-- 会议查询 -->
<template>
    <div class="meeting-search-container">
        <CollapsibleSearchPanel
            :queryParams="queryParams"
            :queryConfig="queryConfig"
            :isDot="isDot"
            :navItems="navItems"
            v-model="navActiveName"
            @navChange="handleNavChange"
            @search="handleSearch"
            @reset="handleReset"
        >
        </CollapsibleSearchPanel>
        <el-table
            class="meeting-search-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            height="calc(100vh - 172px)"
            empty-text="无会议"
        >
            <el-table-column
                label="序号"
                align="center"
                width="60"
                prop="index"
            >
            </el-table-column>
            <el-table-column
                label="会议时间"
                align="left"
                header-align="center"
                width="140"
                prop="startTime"
            >
            </el-table-column>
            <el-table-column
                align="left"
                prop="meetingTitle"
                label="会议名称"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    <div>
                        <el-button
                            class="ellipsis-text"
                            type="text"
                            @click="handleMeetingClick(scope.row)"
                            :style="{
                                'max-width': isMinutesFinished(scope.row)
                                    ? 'calc(100% - 15px)'
                                    : '100%'
                            }"
                        >
                            {{ scope.row.meetingTitle }}
                        </el-button>
                        <i
                            v-if="isMinutesFinished(scope.row)"
                            class="el-icon-document"
                            style="color: #3370ff"
                        ></i>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="meetingType"
                label="会议类型"
                align="center"
                width="150"
            >
            </el-table-column>
            <el-table-column
                prop="organizer"
                label="组织人"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="meetingStatus"
                label="会议状态"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="minutesStatus"
                label="会议纪要状态"
                align="center"
                width="140"
            >
            </el-table-column>
        </el-table>
        <pagination
            class="pagination"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @pagination="getList"
        />
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import Pagination from 'wtf-core-vue/src/components/Pagination';
import CollapsibleSearchPanel from 'Components/CollapsibleSearchPanel.vue';
import { getUserAccount } from '../../commonFunction';
import { queryParams, queryConfig, navItems } from './formInit.js';

export default {
    name: 'MeetingSearch',
    components: { Pagination, CollapsibleSearchPanel },
    props: {
        // 当前激活的页签名称
        activeTab: {
            type: String,
            default: ''
        },
        // 刷新触发器
        shouldRefresh: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            // 导航栏激活项
            navActiveName: '我参加的',
            // 查询参数
            queryParams: { ...queryParams },
            // 导航栏配置
            navItems,
            // 所有关联项目
            relatedProjectsOptions: [],
            searchOptions: [],
            CONSTANTS,
            tableList: [],
            total: 0,
            page: 1,
            limit: 50
        };
    },
    computed: {
        // 搜索面板红点状态
        isDot() {
            const { currentPage, pageSize, dateRange, type, ...restParams } =
                this.queryParams;
            const isValidDate = Array.isArray(dateRange) && dateRange[0];
            return (
                Object.entries(restParams).some(([_, value]) => value !== '') ||
                !!isValidDate
            );
        },
        // 动态查询配置（包含关联项目）
        queryConfig() {
            const config = { ...queryConfig };
            const relateProjectConfig = config.items.find(
                (i) => i.modelKey === 'projectId'
            );
            relateProjectConfig.elSelectAttrs['remote-method'] =
                this.remoteMethod;
            relateProjectConfig.elOptions = this.searchOptions.map((item) => ({
                label: item.projectName,
                value: item.projectId
            }));
            return config;
        }
    },
    watch: {
        // 监听页签切换和刷新触发器
        shouldRefresh(newVal, oldVal) {
            // 只有当前页签是"会议列表"且刷新计数器发生变化时才执行查询
            if (this.activeTab === 'MeetingSearch' && newVal !== oldVal) {
                this.getList();
            }
        }
    },
    created() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    activated() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    methods: {
        // 导航栏切换事件
        handleNavChange(item) {
            // 根据导航项设置查询参数
            if (item.queryField && item.field !== '') {
                this.queryParams[item.queryField] = item.field;
            } else {
                // 清空所有导航相关的查询参数
                this.queryParams.type = '';
            }
            this.page = 1;
            this.getList();
        },
        // 搜索事件
        handleSearch() {
            this.page = 1;
            this.getList();
        },
        // 重置事件
        handleReset() {
            this.queryParams = { ...queryParams };
            this.navActiveName = '';
            this.page = 1;
            this.limit = 50;
            this.getList();
        },
        async getList() {
            const api = this.$service.feature.meeting.getMeetingList;
            const params = {
                ...this.queryParams,
                currentPage: this.page,
                startDate: this.queryParams.dateRange
                    ? this.queryParams.dateRange[0]
                    : '',
                endDate: this.queryParams.dateRange
                    ? this.queryParams.dateRange[1]
                    : '',
                myAccount: getUserAccount(this),
                pageSize: this.limit,
                meetingMinutesStatus: this.queryParams.meetingMinutesStatus,
                meetingStatus: this.queryParams.meetingStatus,
                meetingTitle: this.queryParams.meetingTitle,
                meetingType: this.queryParams.meetingType,
                projectId: this.queryParams.projectId,
                type: this.queryParams.type || ''
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    const { startRow, total } = res.body;
                    this.total = total;
                    this.tableList = res.body.list.map((item, index) => {
                        if (startRow) {
                            item.index = startRow + index;
                        }
                        return item;
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },

        handleMeetingClick(row) {
            this.$router.push({
                path: 'meetingDetail',
                query: { id: row.meetingId }
            });
        },

        /**
         * 获取所有项目用于下拉框选项
         */
        async getRelatedProjectsOptions() {
            try {
                const api =
                    this.$service.department.naturalResources.getProjectselect;
                const res = await api();
                if (res.head.code === '000000') {
                    this.relatedProjectsOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 远程搜索，解决过量数据卡顿问题
         * @param {Object} query 参数
         */
        remoteMethod(query) {
            if (query !== '') {
                this.searchOptions = this.relatedProjectsOptions.filter(
                    (item) => {
                        return item.projectName.indexOf(query) > -1;
                    }
                );
            } else {
                this.searchOptions = [];
            }
        },
        /**
         * 判断会议是否已结束
         * @param {Object} row 每行数据
         * @return {Boolean} 会议是否已结束
         */
        isMinutesFinished(row) {
            return (
                row.minutesStatus === '任务未关闭' ||
                row.minutesStatus === '任务已关闭' ||
                row.minutesStatus === '审核中'
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.meeting-search-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}

.ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    height: 23px;
    font-size: 14px;
}

// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
