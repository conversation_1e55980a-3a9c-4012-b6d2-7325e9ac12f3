<template>
    <div>
        <el-table
            class="my-meetings-management"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center'
            }"
            :row-style="{ height: '35px!important' }"
            empty-text="无待办任务"
        >
            <el-table-column label="序号" align="center" width="80">
                <template slot-scope="{ $index }">{{ $index + 1 }}</template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="生成时间"
                align="center"
                width="160"
            >
            </el-table-column>
            <el-table-column
                prop="taskModule"
                label="模块"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="itemName"
                label="待办事项"
                align="left"
                min-width="120"
            >
                <template slot-scope="scope">
                    <el-tooltip
                        v-if="scope.row.taskModule === '会议'"
                        class="item"
                        effect="dark"
                        :content="scope.row.itemName"
                        placement="top"
                    >
                        <el-button
                            class="ellipsis-text"
                            type="text"
                            @click="handleMeetingClick(scope.row)"
                        >
                            {{ scope.row.itemName }}
                        </el-button>
                    </el-tooltip>
                    <div v-else class="ellipsis-text">
                        {{ scope.row.itemName }}
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="待办任务" header-align="center" align="left" width="320">
                <template slot-scope="{ row }">
                    <el-link
                        v-for="i in row.toDoTaskList"
                        type="primary"
                        style="margin-right: 10px"
                        :key="i"
                        @click="openDialog(i, row.itemId)"
                        >{{ i }}
                    </el-link>
                </template>
            </el-table-column>
        </el-table>
        <MeetingUpdate
            :visible.sync="meetingUpdateVisible"
            :title="meetingUpdateTitle"
            :meetingId="meetingId"
        ></MeetingUpdate>
        <MeetingMinutes
            :visible.sync="meetingMinutesVisible"
            :title="meetingMinutesTitle"
            :meetingId="meetingId"
        ></MeetingMinutes>
        <PreReviewOpinion
            :visible.sync="preReviewOpinionVisible"
            :meetingId="meetingId"
            :title="operation"
        ></PreReviewOpinion>
        <OnlineReviewOpinion
            :visible.sync="onlineReviewOpinionVisible"
            :meetingId="meetingId"
        ></OnlineReviewOpinion>
        <MeetingQuality
            :visible.sync="meetingQualityVisible"
            :meetingId="meetingId"
        ></MeetingQuality>
        <CancelMeetingDialog
            :visible.sync="cancelMeetingVisible"
            :meetingId="meetingId"
            @success="handleCancelSuccess"
        ></CancelMeetingDialog>
    </div>
</template>
<script>
import MeetingUpdate from '../modals/meetingUpdate';
import MeetingMinutes from '../modals/MeetingMinutes';
import PreReviewOpinion from '../modals/PreReviewOpinion';
import OnlineReviewOpinion from '../modals/OnlineReviewOpinion';
import MeetingQuality from '../modals/MeetingQuality';
import CancelMeetingDialog from '../modals/CancelMeetingDialog';

export default {
    name: 'MyMeetingsManagement',
    components: {
        MeetingUpdate,
        MeetingMinutes,
        PreReviewOpinion,
        OnlineReviewOpinion,
        MeetingQuality,
        CancelMeetingDialog
    },
    props: {
        tableList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            meetingId: '',
            operation: '',
            meetingUpdateVisible: false,
            meetingUpdateTitle: '',
            meetingMinutesVisible: false,
            meetingMinutesTitle: '',
            preReviewOpinionVisible: false,
            onlineReviewOpinionVisible: false,
            meetingQualityVisible: false,
            cancelMeetingVisible: false
        };
    },
    watch: {
        // 关闭弹窗时通知父组件刷新
        meetingUpdateVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        },
        meetingMinutesVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        },
        preReviewOpinionVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        },
        onlineReviewOpinionVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        },
        meetingQualityVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        },
        cancelMeetingVisible(newVal) {
            if (!newVal) {
                this.$emit('update');
            }
        }
    },
    methods: {
        openDialog(operation, meetingId) {
            this.meetingId = meetingId;
            this.operation = operation;
            if (operation === '创建会议' || operation === '编辑会议') {
                this.meetingUpdateVisible = true;
                this.meetingUpdateTitle = operation;
            } else if (
                operation === '创建纪要' ||
                operation === '编辑纪要' ||
                operation === '审核会议纪要'
            ) {
                this.meetingMinutesVisible = true;
                this.meetingMinutesTitle = operation;
            } else if (operation === '取消会议') {
                this.cancelMeetingVisible = true;
            } else if (operation === '结束无纪要') {
                this.endMeeting(meetingId);
            } else if (operation.includes('预审')) {
                this.preReviewOpinionVisible = true;
            } else if (operation.includes('线上评审')) {
                this.onlineReviewOpinionVisible = true;
            } else if (operation === '反馈会议质量') {
                this.meetingQualityVisible = true;
            }
        },
        /**
         * 取消会议成功回调
         */
        handleCancelSuccess() {
            this.$emit('update');
        },
        /**
         * 结束会议
         * @param {String} meetingId 会议id
         */
        async endMeeting(meetingId) {
            try {
                await this.$confirm(
                    '确定要结束会议且不生成会议纪要吗？',
                    '提示',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                const api = this.$service.feature.meeting.cancelMeeting;
                const res = await api({
                    operateName: '结束无纪要',
                    meetingId,
                });
                if (res.head.code === '000000') {
                    this.$message.closeAll();
                    this.$message.success('会议已结束');
                    this.$emit('update');
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 点击会议跳出链接
         * @param {Object} row 行
         */
        handleMeetingClick(row) {
            this.$router.push({
                path: 'meetingDetail',
                query: { id: row.itemId, from: 'dashBoard' }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    max-width: 100%;
    height: 23px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
.my-meetings-management {
    border: 1px solid #8c8c8c !important;
}
</style>
