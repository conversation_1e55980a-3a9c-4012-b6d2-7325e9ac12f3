<template>
    <div>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="我的会议" name="MyMeetings" lazy>
                    <MyMeetingsQueryList
                        :active-tab="activeName"
                        :should-refresh="shouldRefresh"
                        class="query-list"
                    ></MyMeetingsQueryList>
                </el-tab-pane>
                <el-tab-pane label="会议室查询" name="MeetingRoomQuery" lazy>
                    <BookDraggableRoom
                        :active-tab="activeName"
                        :should-refresh="shouldRefresh"
                    ></BookDraggableRoom>
                </el-tab-pane>
                <el-tab-pane
                    label="参会人员查询"
                    name="JudgeAttendanceSearch"
                    lazy
                >
                    <JudgeAttendanceQuery
                        :active-tab="activeName"
                        :should-refresh="shouldRefresh"
                    ></JudgeAttendanceQuery>
                </el-tab-pane>
                <el-tab-pane label="会议列表" name="MeetingSearch" lazy>
                    <MeetingSearch
                        :active-tab="activeName"
                        :should-refresh="shouldRefresh"
                    ></MeetingSearch>
                </el-tab-pane>

                <el-tab-pane label="会议任务" name="MeetingTaskTracker" lazy>
                    <MeetingTaskTrace
                        :active-tab="activeName"
                        :should-refresh="shouldRefresh"
                    ></MeetingTaskTrace>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import JudgeAttendanceQuery from './components/judgeAttendanceQuery';
import MeetingSearch from './components/meetingSearch';
import MeetingTaskTrace from './components/meetingTaskTrace';
import MyMeetingsQueryList from './components/myMeetings/MyMeetingsQueryList.vue';
import BookDraggableRoom from './components/BookDraggableRoom';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingManagement',
    components: {
        JudgeAttendanceQuery,
        MeetingSearch,
        MeetingTaskTrace,
        MyMeetingsQueryList,
        BookDraggableRoom
    },
    data() {
        return {
            activeName: 'MyMeetings',
            // 用于触发子组件刷新的计数器
            shouldRefresh: 0
        };
    },
    watch: {
        activeName() {
            // 当页签切换时，增加刷新计数器来触发子组件更新
            this.shouldRefresh += 1;
        }
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
        getExternalStaffPeople(this);
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
    height: 100vh;
    overflow: auto;
}
::v-deep #pane-second {
    border: 1px solid #8c8c8c !important;
}
</style>
