<!-- 人员参会查询 -->
<template>
    <div class="judge-attendance-container">
        <div class="flex">
            <div class="query-container">
                <div class="flex">
                    <el-date-picker
                        class="selector date-selector"
                        v-model="week"
                        type="week"
                        placeholder="选择周"
                        ref="dataPicker"
                        :format="dateFormat"
                        :clearable="false"
                        :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                    <el-button
                        class="qucik-access"
                        type="primary"
                        @click="handleQuickAccess('本周')"
                        >本周</el-button
                    >
                    <el-button type="primary" @click="handleQuickAccess('下周')"
                        >下周</el-button
                    >
                    <PeopleSelector
                        class="selector"
                        placeholder="人员"
                        v-model="accountList"
                        :options="externalOptions"
                        @click.native="handlePeopleSelect"
                        style="margin-left: 10px"
                        :isCollapseTags="true"
                    ></PeopleSelector>
                    <el-select
                        v-model="departmentList"
                        class="selector"
                        style="margin-left: 10px"
                        collapse-tags
                        placeholder="部门"
                        multiple
                        readonly
                        remote
                        filterable
                        @click.native="handleDepartmentSelect"
                    >
                        <el-option
                            v-for="item in departmentDisplayOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </div>

            <el-button class="action-button" type="primary" @click="getList"
                >查询</el-button
            >
            <el-button class="action-button" type="primary" @click="handleReset"
                >重置</el-button
            >
        </div>
        <el-table
            class="judge-attendance-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            empty-text="无评委参会数据"
            height="calc(100vh - 120px)"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                header-align="center"
                align="center"
                prop="firstOrgName"
                label="一级部门"
                width="110"
            >
            </el-table-column>

            <el-table-column
                prop="userName"
                label="人员"
                align="center"
                width="80"
            >
            </el-table-column>

            <el-table-column
                v-for="(i, index) in dateArr"
                :label="i"
                :key="i"
                align="center"
            >
                <el-table-column
                    :key="weekList[index]"
                    :label="weekList[index]"
                    align="center"
                >
                    <el-table-column
                        v-for="j in timePeriodList"
                        :key="j"
                        :label="j"
                        align="center"
                        min-width="60"
                    >
                        <template slot-scope="scope">
                            <el-popover
                                placement="top-start"
                                width="500"
                                trigger="hover"
                                :disabled="
                                    handleWeekData(
                                        scope.row,
                                        dateArr[index],
                                        j,
                                        'meetingList'
                                    ).length === 0 &&
                                    handleWeekData(
                                        scope.row,
                                        dateArr[index],
                                        j,
                                        'unavailableList'
                                    ).length === 0
                                "
                            >
                                <div
                                    slot="reference"
                                    :style="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'style'
                                        )
                                    "
                                    class="cell-content"
                                >
                                    {{
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'numbers'
                                        ) || ''
                                    }}
                                </div>
                                <!-- 会议数据表格 -->
                                <div
                                    v-if="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'meetingList'
                                        ).length > 0
                                    "
                                >
                                    <h4 style="margin: 0 0 10px 0; color: #333">
                                        会议安排
                                    </h4>
                                    <el-table
                                        style="width: 100%; margin-bottom: 15px"
                                        :header-cell-style="{
                                            'text-align': 'center'
                                        }"
                                        :data="
                                            handleWeekData(
                                                scope.row,
                                                dateArr[index],
                                                j,
                                                'meetingList'
                                            )
                                        "
                                    >
                                        <el-table-column
                                            prop="startTime"
                                            label="预计开始时间"
                                            min-width="100"
                                            align="center"
                                        >
                                        </el-table-column>
                                        <el-table-column
                                            prop="endTime"
                                            label="预计完成时间"
                                            min-width="100"
                                            align="center"
                                        >
                                        </el-table-column>
                                        <el-table-column
                                            prop="meetingTitle"
                                            label="会议名称"
                                            min-width="200"
                                        >
                                        </el-table-column>
                                    </el-table>
                                </div>

                                <!-- 不可用时间表格 -->
                                <div
                                    v-if="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'unavailableList'
                                        ).length > 0
                                    "
                                >
                                    <h4 style="margin: 0 0 10px 0; color: #333">
                                        不可用时间
                                    </h4>
                                    <el-table
                                        style="width: 100%"
                                        :header-cell-style="{
                                            'text-align': 'center',
                                            'background-color': '#3370ff',
                                            'color': '#fff'
                                        }"
                                        :data="
                                            handleWeekData(
                                                scope.row,
                                                dateArr[index],
                                                j,
                                                'unavailableList'
                                            )
                                        "
                                    >
                                        <el-table-column
                                            label="时间"
                                            width="120"
                                            align="center"
                                        >
                                            <template
                                                slot-scope="unavailableScope"
                                            >
                                                {{
                                                    formatUnavailableTime(
                                                        unavailableScope.row
                                                    )
                                                }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            prop="reason"
                                            label="事由"
                                            min-width="100"
                                            header-align="center"
                                            align="left"
                                        >
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table-column>
        </el-table>
        <PeopleSelectorWithOrg
            :visible.sync="peopleSelectorWithOrgDialogVisible"
            :value="selectedPeopleForDialog"
            @confirm="handleAddPeopleByOrg"
        ></PeopleSelectorWithOrg>
        <!-- 部门选择弹窗 -->
        <DepartmentSelector
            :visible.sync="departmentSelectorVisible"
            :selectedDepartments="departmentList"
            :treeData="departmentTreeData"
            @confirm="handleDepartmentConfirm"
        />
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import moment from 'moment';
import PeopleSelector from 'Components/PeopleSelector.vue';
import { getDaysOfWeek } from '../../commonFunction';
import PeopleSelectorWithOrg from '../modals/PeopleSelectorWithOrg.vue';
import DepartmentSelector from '../modals/DepartmentSelector.vue';

const weekList = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];
const timePeriodList = ['上午', '下午'];
export default {
    name: 'JudgeAttendanceQuery',
    components: { PeopleSelector, PeopleSelectorWithOrg, DepartmentSelector },
    props: {
        // 当前激活的页签名称
        activeTab: {
            type: String,
            default: ''
        },
        // 刷新触发器
        shouldRefresh: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            week: new Date(),
            pickerOptions: {
                firstDayOfWeek: 1
            },
            CONSTANTS,
            tableList: [],
            dateArr: [],
            weekList,
            timePeriodList,
            accountList: [],
            // 人员选择弹窗
            peopleSelectorWithOrgDialogVisible: false,
            // 部门选择弹窗
            departmentSelectorVisible: false,
            // 部门列表 - 存储选中的部门orgCode
            departmentList: [],
            // 部门选择的完整节点信息
            selectedDepartmentNodes: [],
            // 部门下拉框选项
            departmentTreeData: [],
            // 选择的一级部门
            firstOrgCodeList: [],
            // 选择的二级部门
            secondOrgCodeList: []
        };
    },
    computed: {
        dateFormat() {
            const startOfWeek = moment(this.week)
                .startOf('week')
                .format('YYYY/M/D');
            const endOfWeek = moment(this.week)
                .endOf('week')
                .format('YYYY/M/D');
            return `${startOfWeek} - ${endOfWeek}`;
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        },
        // 部门显示选项 - 只显示二级和三级部门
        departmentDisplayOptions() {
            return this.selectedDepartmentNodes
                .filter((node) => {
                    return node.level === 2 || node.level === 3;
                })
                .map((node) => ({
                    value: node.orgCode,
                    label: node.orgName
                }));
        },
        // 为弹窗组件准备的已选中人员数据
        selectedPeopleForDialog() {
            if (!this.accountList || this.accountList.length === 0) {
                return [];
            }

            // 根据accountList从externalOptions中找到对应的完整人员信息
            return this.accountList
                .map((account) => {
                    const person = this.externalOptions.find(
                        (item) => item.loginName === account
                    );
                    if (person) {
                        // 只需要传递account，组件内部会匹配完整信息
                        return {
                            account: person.loginName,
                            name: person.employeeName
                        };
                    }
                    return null;
                })
                .filter(Boolean); // 过滤掉null值
        }
    },
    watch: {
        // 监听页签切换和刷新触发器
        shouldRefresh(newVal, oldVal) {
            // 只有当前页签是"参会人员查询"且刷新计数器发生变化时才执行查询
            if (
                this.activeTab === 'JudgeAttendanceSearch' &&
                newVal !== oldVal
            ) {
                this.getList();
            }
        }
    },
    mounted() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getTreeData();
        this.getList();
    },
    activated() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getTreeData();
        this.getList();
    },
    methods: {
        /**
         * 处理部门选择确认
         * @param {Object} value - 包含keys和nodes的对象
         */
        handleDepartmentConfirm(value) {
            // 部门与人员不能同时选择
            this.accountList = [];

            const { nodes } = value;

            // 过滤出二级和三级部门
            const filteredNodes = nodes.filter((node) => {
                // 使用层级信息过滤：只要二级部门(level=2)和三级部门(level=3)
                return node.level === 2 || node.level === 3;
            });
            this.firstOrgCodeList = nodes
                .filter((node) => node.level === 2)
                .map((node) => node.orgCode);
            this.secondOrgCodeList = nodes
                .filter((node) => node.level === 3)
                .map((node) => node.orgCode);
            // 更新选中的部门列表和节点信息
            this.departmentList = filteredNodes.map((node) => node.orgCode);
            this.selectedDepartmentNodes = filteredNodes;
        },
        handleDepartmentSelect() {
            this.departmentSelectorVisible = true;
        },
        handleAddPeopleByOrg(peopleList) {
            // 部门与人员不能同时选择
            this.departmentList = [];
            this.selectedDepartmentNodes = [];
            this.firstOrgCodeList = [];
            this.secondOrgCodeList = [];

            this.accountList = peopleList.map((i) => i.account);
        },
        handlePeopleSelect() {
            this.peopleSelectorWithOrgDialogVisible = true;
        },
        /**
         * 重置
         */
        handleReset() {
            this.accountList = [];
            this.departmentList = [];
            this.firstOrgCodeList = [];
            this.secondOrgCodeList = [];
            this.selectedDepartmentNodes = [];
            this.handleQuickAccess('本周');
            this.getList();
        },
        /**
         * 获取列表
         */
        async getList() {
            const api = this.$service.feature.meeting.getJudgeAttendanceInfo;
            const startDate = moment(this.week)
                .startOf('week')
                .format('YYYY-MM-DD');
            const endDate = moment(this.week)
                .endOf('week')
                .format('YYYY-MM-DD');
            const params = {
                currentPage: this.page,
                endDate,
                accountList: this.accountList,
                departmentList: this.departmentList,
                pageSize: this.limit,
                // 选择的一级部门
                firstOrgCodeList: this.firstOrgCodeList,
                // 选择的二级部门
                secondOrgCodeList: this.secondOrgCodeList,
                startDate
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
                    this.tableList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },
        /**
         * 获取周数据的基础信息
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {Object} 包含会议和不可用时间的基础信息
         */
        getWeekDataInfo(rowData, curDay, period) {
            const {
                // 评委登记的不可用时间段
                judgesUnavailableTimeList = [],
                // 已经有的会议占用的时间段
                meetingJudgesPartVoList = []
            } = rowData;

            // 检查是否有不可用时间
            const validUnavailableTimeList =
                this.isCurrnetUnavailableTimeInPeriod(
                    judgesUnavailableTimeList,
                    curDay,
                    period
                );

            // 检查是否有会议
            const curMeetingData = meetingJudgesPartVoList.filter(
                (i) => i.dateVal === curDay
            );

            const hasMeeting = curMeetingData.length > 0;
            const hasUnavailableTime = validUnavailableTimeList.length > 0;

            // 获取会议数量和列表
            let meetingCount = 0;
            let meetingList = [];
            if (hasMeeting) {
                meetingCount =
                    period === '上午'
                        ? curMeetingData[0].morningCount
                        : curMeetingData[0].afternoonCount;
                meetingList =
                    period === '上午'
                        ? curMeetingData[0].morningMeetList
                        : curMeetingData[0].afternoonMeetList;
            }

            return {
                hasMeeting,
                hasUnavailableTime,
                meetingCount,
                meetingList,
                validUnavailableTimeList
            };
        },

        /**
         * 获取周数据的样式对象
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {Object} 样式对象
         */
        getWeekDataStyle(rowData, curDay, period) {
            const { hasMeeting, hasUnavailableTime, meetingCount } =
                this.getWeekDataInfo(rowData, curDay, period);

            if (hasUnavailableTime && hasMeeting && meetingCount > 0) {
                // 不可用时间和会议重合：红色背景
                return {
                    backgroundColor: '#ff4d4f',
                    color: '#fff',
                    padding: '4px'
                };
            } else if (hasMeeting && meetingCount > 0) {
                // 只有会议：绿色背景
                return {
                    backgroundColor: '#92d050',
                    color: '#fff',
                    padding: '4px'
                };
            } else if (hasUnavailableTime) {
                // 只有不可用时间：橙色背景
                return {
                    backgroundColor: '#ffc000',
                    color: '#fff',
                    padding: '4px'
                };
            }
            return {};
        },

        /**
         * 获取周数据的显示数字
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {String} 显示的数字字符串
         */
        getWeekDataNumbers(rowData, curDay, period) {
            const { hasMeeting, hasUnavailableTime, meetingCount } =
                this.getWeekDataInfo(rowData, curDay, period);

            if (hasUnavailableTime && hasMeeting && meetingCount > 0) {
                // 不可用时间和会议重合：显示会议数量
                return String(meetingCount);
            } else if (hasMeeting && meetingCount > 0) {
                // 只有会议：显示会议数量
                return String(meetingCount);
            } else if (hasUnavailableTime) {
                // 只有不可用时间：不显示内容
                return '';
            }
            return '';
        },

        /**
         * 获取周数据的表格列表（合并会议和不可用时间）
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {Array} 表格数据数组
         */
        getWeekDataTableList(rowData, curDay, period) {
            const {
                hasMeeting,
                hasUnavailableTime,
                meetingList,
                validUnavailableTimeList
            } = this.getWeekDataInfo(rowData, curDay, period);

            if (hasUnavailableTime && hasMeeting) {
                // 合并不可用时间和会议数据
                return [...validUnavailableTimeList, ...meetingList];
            } else if (hasMeeting) {
                return meetingList || [];
            } else if (hasUnavailableTime) {
                return validUnavailableTimeList || [];
            }
            return [];
        },

        /**
         * 获取周数据的会议列表
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {Array} 会议数据数组
         */
        getWeekDataMeetingList(rowData, curDay, period) {
            const { meetingList } = this.getWeekDataInfo(
                rowData,
                curDay,
                period
            );
            return meetingList || [];
        },

        /**
         * 获取周数据的不可用时间列表
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @returns {Array} 不可用时间数据数组
         */
        getWeekDataUnavailableList(rowData, curDay, period) {
            const { validUnavailableTimeList } = this.getWeekDataInfo(
                rowData,
                curDay,
                period
            );
            return validUnavailableTimeList || [];
        },

        /**
         * 处理周数据，根据类型调用对应的专门函数（保持向后兼容）
         * @param {Object} rowData 行数据
         * @param {String} curDay 当前日期
         * @param {String} period 上午/下午
         * @param {String} type 类型 'numbers'|'tableList'|'style'|'meetingList'|'unavailableList'
         * @returns {Object|String|Array} 根据type返回对应类型的数据
         * @deprecated 建议直接使用对应的专门函数
         */
        handleWeekData(rowData, curDay, period, type) {
            switch (type) {
                case 'style':
                    return this.getWeekDataStyle(rowData, curDay, period);
                case 'numbers':
                    return this.getWeekDataNumbers(rowData, curDay, period);
                case 'tableList':
                    return this.getWeekDataTableList(rowData, curDay, period);
                case 'meetingList':
                    return this.getWeekDataMeetingList(rowData, curDay, period);
                case 'unavailableList':
                    return this.getWeekDataUnavailableList(
                        rowData,
                        curDay,
                        period
                    );
                default:
                    return {};
            }
        },
        /**
         * 判断不可用时间列表中是否存在与指定日期和时间段重合的项目
         * @param {Array} unavailableTimeList 不可用时间列表
         * @param {String} curDay 当前日期 格式：2025-01-10
         * @param {String} period 时间段 "上午"或"下午"
         * @param {String} type 返回类型 "numbers"返回空，其他返回匹配的项目列表
         * @returns {null|Array} type为"numbers"时返回null，否则返回重合的不可用时间项目
         */
        isCurrnetUnavailableTimeInPeriod(
            unavailableTimeList,
            curDay,
            period,
            type
        ) {
            if (!unavailableTimeList || unavailableTimeList.length === 0) {
                return type === 'numbers' ? null : [];
            }

            // 定义目标时间段
            let targetStartTime;
            let targetEndTime;
            if (period === '上午') {
                targetStartTime = '08:00';
                targetEndTime = '12:00';
            } else if (period === '下午') {
                targetStartTime = '12:00';
                targetEndTime = '18:00';
            } else {
                return type === 'numbers' ? null : [];
            }

            // 创建目标时间段的moment对象
            const targetStart = moment(`${curDay} ${targetStartTime}`);
            const targetEnd = moment(`${curDay} ${targetEndTime}`);

            // 筛选出与目标时间段重合的不可用时间项
            const matchingItems = unavailableTimeList.filter((item) => {
                // 创建不可用时间段的moment对象
                const unavailableStart = moment(
                    `${item.startDate} ${item.startTime}`
                );
                const unavailableEnd = moment(
                    `${item.endDate} ${item.endTime}`
                );

                // 判断时间段是否重合
                // 两个时间段重合的条件：开始时间小于对方的结束时间，且结束时间大于对方的开始时间
                return (
                    unavailableStart.isBefore(targetEnd) &&
                    unavailableEnd.isAfter(targetStart)
                );
            });

            // 根据type返回不同的结果
            if (type === 'numbers') {
                return null;
            }

            return matchingItems;
        },
        /**
         * 格式化不可用时间的日期显示
         * @param {Object} row 不可用时间数据行
         * @returns {String} 格式化后的日期字符串
         */
        formatUnavailableDate(row) {
            if (!row) return '';

            const startDate = moment(row.startDate).format('YYYY/MM/DD');
            const endDate = moment(row.endDate).format('YYYY/MM/DD');

            if (startDate === endDate) {
                return startDate;
            }
            return `${startDate} - ${endDate}`;
        },
        /**
         * 格式化不可用时间的时间显示
         * @param {Object} row 不可用时间数据行
         * @returns {String} 格式化后的时间字符串
         */
        formatUnavailableTime(row) {
            if (!row) return '';

            if (row.allDayFlag === '全天') {
                return '全天';
            }
            return `${row.startTime} - ${row.endTime}`;
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Function} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理第一列的合并
            if (![0].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'firstOrgName';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.tableList.length; i++) {
                    if (this.tableList[i][compareField] === row[compareField]) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.tableList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (prevRow[compareField] === row[compareField]) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.tableList.length; i++) {
                if (this.tableList[i][compareField] === row[compareField]) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        },
        async getTreeData() {
            const params = {};
            const api =
                this.$service.feature.meetingTaskTrace.getDepartmentTree;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.departmentTreeData = [
                    {
                        orgCode: 'tech-center',
                        orgName: '技术中心',
                        children: res.body
                    }
                ];
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.judge-attendance-container {
    height: calc(100vh - 65px);
}

.query-container {
    display: flex;
    gap: 10px;
    width: calc(100% - 140px);
}

.selector {
    width: 220px;
}

.date-selector {
    height: 28px;
}

@media (max-width: 568px) {
    .selector {
        flex: 1 1 100%;
    }
}
// 修改placeholder颜色
::v-deep .selector .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
.qucik-access {
    margin-left: 10px;
}
.action-button {
    height: 28px;
    margin-left: 15px;
}

.judge-attendance-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

::v-deep .el-table--mini .el-table__cell {
    padding: 0 !important;
}
::v-deep td .cell {
    padding: 0 !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}

.cell-content {
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
}
</style>
