<template>
    <div class="product-list">
        <div
            class="product-card"
            v-for="(item, index) in productList"
            :key="index"
            :style="item.style"
        >
            <div
                :class="`product-item ${item.class}`"
                v-for="dataItem in productList[index].data"
                :key="dataItem.productType"
            >
                <el-popover
                    class="product-item-content"
                    placement="right"
                    trigger="hover"
                    ref="productListPopover"
                    width="500"
                    v-if="dataItem.businessProjectVos"
                    :disabled="popoverDisabled"
                >
                    <div class="project-list">
                        <span
                            v-for="project in dataItem.businessProjectVos"
                            :key="project.projectNumber"
                            style="display: flex"
                        >
                            <el-badge
                                :value="
                                    getBadgeValue(
                                        project.projectStatus,
                                        project.hrCost
                                    )
                                "
                                style="padding: 0 8px"
                                :style="{
                                    '--project-status-color': setProductColor(
                                        project.projectStatus
                                    )
                                }"
                                class="area-badge"
                            >
                                <div style="display: flex; padding: 5px">
                                    <span
                                        class="title-prefix"
                                        :style="{
                                            backgroundColor:
                                                project.strategyColor
                                        }"
                                    >
                                        {{ project.strategy }} </span
                                    ><el-link
                                        type="primary"
                                        @click="
                                            gotoProjectDetail(project.projectId)
                                        "
                                        style="
                                            height: 1.3vw;
                                            line-height: 1.3w;
                                            font-size: 0.9vw;
                                        "
                                        >{{ project.projectName }}</el-link
                                    >
                                </div>
                            </el-badge>
                        </span>
                    </div>
                    <div slot="reference">
                        <span>{{ dataItem.productType }}</span>
                        <i class="el-icon-arrow-right"></i>
                    </div>
                </el-popover>
                <span v-if="!dataItem.businessProjectVos">{{
                    dataItem.productType
                }}</span>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: '',
    components: {},
    props: {
        config: {
            type: Array,
            default() {
                return [];
            }
        },
        businessUnit: {
            type: String,
            default: ''
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // 产品分类列表
            productList: [],
            popoverDisabled: false
        };
    },
    watch: {
        list(newVal) {
            if (newVal.length !== 0) {
                this.getProductListData(this.list);
            } else {
                this.productList = [];
            }
        }
    },
    activated() {
        this.popoverDisabled = false;
    },
    methods: {
        // 获取产品列表数据
        getProductListData(list) {
            this.productList = this.config.map((i) => {
                i.class = '';
                if (i.name === '整机终端') {
                    i.class = 'col-2';
                }
                i.data = list.find(
                    (l) => l.productSet === i.name
                ).productTypeVos;

                i.data.forEach((j) => {
                    if (j.businessProjectVos) {
                        j.businessProjectVos.forEach((k) => {
                            k.strategy = k.strategy[0];
                            k.strategyColor =
                                k.strategy === '战' ? '#00baad' : '#2a82e4';
                            if (k.projectStatus === '在研') {
                                k.projectStatusColor = '#3fcd7b';
                            } else if (k.projectStatus === '发布') {
                                k.projectStatusColor = '#2a82e4';
                            } else {
                                k.projectStatusColor = '#ff8d1a';
                            }
                        });
                    }
                });
                return i;
            });
        },
        /**
         * 跳转至项目详情页面
         * @param {String} projectId 项目编号
         * @param {String} projectName 项目名称
         */
        async gotoProjectDetail(projectId) {
            const valid = projectId;
            if (!valid) {
                this.$message.warning('该项目暂未录入系统');
                return;
            }
            const popoverList = [];
            for (const popover of this.$refs.productListPopover) {
                popoverList.push(popover);
            }
            await Promise.all(popoverList.map((popover) => popover.doClose()));
            this.popoverDisabled = true;
            this.$router.push({
                path: '/project/baseInfo',
                query: {
                    id: projectId,
                    from: 'scene'
                }
            });
        },
        setProductColor(status) {
            if (status === '在研') {
                return '#3fcd7b';
            } else if (status === '发布') {
                return '#2a82e4';
            }
            return '#ff8d1a';
        },
        /**
         * 不显示除在研之外状态的人力
         * @param {String} projectStatus 项目状态
         * @param {String} hrCost 人力
         * @returns {String} 描述
         */
        getBadgeValue(projectStatus, hrCost) {
            if (projectStatus !== '在研') {
                return projectStatus;
            }
            return `${projectStatus}，投入${hrCost}人月`;
        }
    }
};
</script>
<style lang="scss" scoped>
.product-list {
    width: 30% !important;
    display: flex;
    .product-card {
        height: 452px;
        overflow-y: auto;
        background: url(~scene/assets/category-bg.png) no-repeat;
        background-size: 100% 100%;
        margin-right: 20px;
        padding: 0 32px;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        justify-content: space-between;
        border-radius: 0px;
        .product-item {
            width: 100%;
            height: 12.5%;
            display: flex;
            align-items: center;
            border-bottom: 2px dashed #a6a6a6;
            font-size: 24px;
            cursor: pointer;
            .product-item-content {
                width: 100%;
                display: flex;
                align-items: center;
            }
            ::v-deep .el-popover__reference-wrapper {
                width: 100%;
                height: 100%;
                .el-popover__reference {
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            }
        }
        .product-item.col-2 {
            width: 50%;
        }
        .product-item.col-2:nth-child(2n + 1) {
            width: 46%;
        }
    }
    .product-card:last-child {
        margin-right: 0;
    }
}
.project-list {
    max-width: 500px;
    max-height: 400px;
    overflow: auto;
}
@media screen and (max-width: 1600px) {
    .project-list {
        max-width: 500px;
    }
}
.projectStatus {
    width: 30px;
}
.area-badge {
    margin-top: 8px;
    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: var(--project-status-color);
        font-size: 0.8vw;
    }
}
.title-prefix {
    width: 1.3vw;
    height: 1.3vw;
    color: white;
    border-radius: 50%;
    font-size: 0.8vw;
    line-height: 1.3vw;
    text-align: center;
    margin-right: 5px;
}
</style>
